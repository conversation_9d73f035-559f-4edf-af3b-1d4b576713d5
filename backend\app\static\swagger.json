{"swagger": "2.0", "info": {"version": "1.0.0", "title": "Analysis App API", "description": "API documentation for the Analysis App"}, "host": "127.0.0.1:7000", "basePath": "/api", "schemes": ["http", "https"], "tags": [{"name": "Data Collection", "description": "Endpoints for uploading and selecting columns from files"}, {"name": "Data Processing", "description": "Endpoints for processing files and analyzing text and other utility needed by the core analysis endpoints"}, {"name": "Frequency Analysis", "description": "Endpoints for word analysis and word cloud generation"}, {"name": "Process", "description": "Endpoints for managing processing tasks and downloading results"}, {"name": "Session Management", "description": "Endpoints for managing user sessions"}, {"name": "LDA Analysis", "description": "Endpoints for LDA topic modeling"}, {"name": "TF-IDF Analysis", "description": "Endpoints for TF-IDF analysis"}, {"name": "N-gram Analysis", "description": "Endpoints for generating N-gram analysis"}, {"name": "Sentiment Analysis", "description": "Endpoints for sentiment and word similarity analysis"}, {"name": "Connection Analysis", "description": "Endpoints for network and connection analysis"}, {"name": "BERT Analysis", "description": "Endpoints for BERT-based topic modeling"}], "paths": {"/api/upload": {"post": {"tags": ["Data Collection"], "summary": "Upload a file", "description": "Uploads a file and returns its columns. Creates a new session if one doesn't exist.", "parameters": [{"name": "file", "in": "formData", "required": true, "type": "file", "description": "The file to upload (CSV or Excel)."}], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "filename": {"type": "string"}, "columns": {"type": "array", "items": {"type": "string"}}, "session_id": {"type": "string", "description": "The session ID"}}}, "400": {"description": "Bad request (e.g., no file uploaded or invalid file type)"}, "500": {"description": "Internal server error"}}}}, "/api/select-columns": {"post": {"tags": ["Data Collection"], "summary": "Select columns from uploaded file", "description": "Selects specific columns from the uploaded file and combines their text.", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"columns": {"type": "array", "items": {"type": "string"}}}}, "description": "The columns to select."}], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data_info": {"type": "object", "properties": {"row_count": {"type": "integer"}, "column_count": {"type": "integer"}, "original_columns": {"type": "array", "items": {"type": "string"}}, "preview": {"type": "array", "items": {"type": "object"}}}}}}}, "400": {"description": "Bad request (e.g., no file uploaded or no columns selected)"}, "404": {"description": "File not found"}, "500": {"description": "Internal server error"}}}}, "/api/language-groups": {"get": {"tags": ["Data Processing"], "summary": "Get available language groups and analyzers", "description": "Returns a list of available language groups and their analyzers.", "responses": {"200": {"description": "Successful response", "schema": {"type": "object"}}}}}, "/api/pos-tags/{language}/{analyzer}": {"get": {"tags": ["Data Processing"], "summary": "Get POS tags for a language and analyzer", "description": "Returns POS tags for the selected language and analyzer.", "parameters": [{"name": "language", "in": "path", "required": true, "type": "string", "description": "The language to get POS tags for."}, {"name": "analyzer", "in": "path", "required": true, "type": "string", "description": "The analyzer to use for POS tagging."}], "responses": {"200": {"description": "Successful response", "schema": {"type": "object"}}}}}, "/api/spacy-status": {"get": {"tags": ["Data Processing"], "summary": "Check spaCy model status", "description": "Checks if the spaCy model is installed.", "responses": {"200": {"description": "Successful response", "schema": {"type": "object"}}}}}, "/api/columns": {"get": {"tags": ["Data Collection"], "parameters": [{"name": "filename", "in": "query", "required": true, "type": "string", "description": "The name of the uploaded file."}, {"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID to filter by. If provided, gets columns from that specific session."}], "produces": ["application/json"], "summary": "Get columns from an uploaded Excel file", "description": "Returns the columns of an uploaded Excel file. Creates a new session if one doesn't exist. Can filter by session_id.", "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "columns": {"type": "array", "items": {"type": "string"}}}}}, "400": {"description": "File not uploaded or not found"}, "404": {"description": "Session not found", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Session with ID xyz123 not found"}}}}, "500": {"description": "Internal server error"}}}}, "/api/process": {"post": {"tags": ["Data Processing"], "summary": "Process an Excel file with NLP analysis", "description": "Processes an uploaded Excel file with NLP analysis. Creates a new session if one doesn't exist. Can filter by session_id.", "parameters": [{"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to process."}, {"name": "language", "in": "formData", "type": "string", "description": "The language to use for processing."}, {"name": "analyzer", "in": "formData", "type": "string", "description": "The analyzer to use for processing."}, {"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID to filter by. If provided, processes file from that specific session."}], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The ID of the task"}, "status": {"type": "string", "description": "The status of the task"}, "message": {"type": "string", "description": "A message about the task"}}}}, "400": {"description": "Bad request", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No file uploaded"}}}}, "404": {"description": "Session not found", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Session with ID xyz123 not found"}}}}, "500": {"description": "Internal server error"}}}}, "/api/progress/{task_id}": {"get": {"tags": ["Process"], "summary": "Get progress of a processing task", "description": "Returns the progress of a specific processing task. Creates a new session if one doesn't exist. Can filter by session_id.", "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string", "description": "The ID of the task to check progress for."}, {"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID to filter by. If provided, checks progress for that specific session."}], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The ID of the task"}, "progress": {"type": "integer", "description": "The progress value (0-100)"}, "session_id": {"type": "string", "description": "The session ID"}}}}, "404": {"description": "Session not found", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Session with ID xyz123 not found"}}}}}}}, "/api/download/{filename}": {"get": {"tags": ["Process"], "summary": "Download a processed file", "description": "Downloads a processed file by filename. Redirects to the unified /files/{filepath} endpoint with download=true. Can filter by session_id.", "parameters": [{"name": "filename", "in": "path", "required": true, "type": "string", "description": "The name of the file to download."}, {"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID to filter by. If provided, downloads file from that specific session."}], "responses": {"200": {"description": "Successful response", "schema": {"type": "file"}}, "400": {"description": "No active session", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No valid session ID found"}}}}, "404": {"description": "File not found or session not found", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "File not found or Session with ID xyz123 not found"}}}}}}}, "/api/result/{task_id}": {"get": {"tags": ["Process"], "summary": "Get Task Result", "description": "Retrieve the result of a completed task by its task ID. Creates a new session if one doesn't exist. Can filter by session_id.", "parameters": [{"name": "task_id", "in": "path", "required": true, "description": "The ID of the task to retrieve the result for.", "schema": {"type": "string"}}, {"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID to filter by. If provided, retrieves result from that specific session."}], "responses": {"200": {"description": "Task result retrieved successfully.", "content": {"application/json": {"example": {"success": true, "task_id": "12345", "status": "completed", "download_url": "/api/download/processed_file.xlsx", "filename": "processed_file.xlsx"}}}}, "400": {"description": "Task is not yet complete."}, "404": {"description": "No output file found or session not found.", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No uploaded file found in session or Session with ID xyz123 not found"}}}}}}}, "/api/analyse/word_data": {"post": {"tags": ["Data Analysis"], "summary": "Get Word Data", "description": "Retrieve word data for manual selection mode.", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"column_name": {"type": "string", "description": "The column name to analyze."}}, "required": ["column_name"]}}}}, "responses": {"200": {"description": "Word data retrieved successfully.", "content": {"application/json": {"example": {"words": ["word1", "word2", "word3"]}}}}, "400": {"description": "Invalid input or missing parameters."}, "404": {"description": "File not found."}}}}, "/api/analyse/analyze": {"post": {"tags": ["Data Analysis"], "summary": "파일 분석 API", "description": "업로드된 파일을 기반으로 텍스트 분석을 수행합니다.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "column_name", "in": "formData", "required": false, "type": "string", "description": "분석할 컬럼명"}, {"name": "selection_type", "in": "formData", "required": false, "type": "string", "default": "top_n", "description": "선택 방식 (top_n, manual 등)"}, {"name": "max_words", "in": "formData", "required": false, "type": "integer", "default": 50, "description": "최대 단어 수"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle", "description": "워드클라우드 형태"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis", "description": "워드클라우드 색상 테마"}, {"name": "selected_words", "in": "formData", "required": false, "type": "string", "description": "JSON array of words to edit. If not provided, all words from the specified column will be used as the base for editing."}], "responses": {"200": {"description": "분석 결과 반환"}, "400": {"description": "잘못된 요청"}, "500": {"description": "서버 오류"}}}}, "/api/analyse/wordcloud": {"post": {"tags": ["Data Analysis"], "summary": "선택 단어 워드클라우드 생성 API", "description": "선택한 단어로 워드클라우드를 생성합니다.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "selected_words", "in": "formData", "required": true, "type": "string", "description": "선택된 단어 리스트 (JSON 배열)"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis"}], "responses": {"200": {"description": "워드클라우드 생성 결과 반환"}, "400": {"description": "입력 오류"}, "500": {"description": "서버 오류"}}}}, "/api/analyse/edit_words": {"post": {"tags": ["Data Analysis"], "summary": "Edit words and re-analyze", "description": "Edit words from the current analysis and re-run the analysis with the updated words.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID. If not provided, current session will be used."}, {"name": "edited_words", "in": "formData", "required": true, "type": "string", "description": "JSON array of word edits, each containing 'original' and 'new' fields"}, {"name": "selected_words", "in": "formData", "required": false, "type": "string", "description": "JSON array of originally selected words before editing"}, {"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to analyze"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle", "description": "Shape of the word cloud visualization"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis", "description": "Color theme for the word cloud"}], "responses": {"200": {"description": "Analysis results with edited words", "schema": {"type": "object", "properties": {"words": {"type": "array", "items": {"type": "string"}, "description": "Updated list of words after editing"}, "download_url": {"type": "string", "description": "URL to download the analysis results"}, "wordcloud_url": {"type": "string", "description": "URL to view the updated word cloud visualization"}}}}, "400": {"description": "Bad request (e.g., missing parameters, invalid JSON)", "schema": {"type": "object", "properties": {"error": {"type": "string"}}}}, "404": {"description": "File not found", "schema": {"type": "object", "properties": {"error": {"type": "string"}}}}, "500": {"description": "Server error", "schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}, "/api/analyse/process": {"post": {"summary": "Process data and perform LDA topic modeling", "tags": ["Data Analysis"], "description": "Executes LDA topic modeling using uploaded file and analysis parameters including text column and topic range.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"type": "object", "properties": {"text_column": {"type": "string", "example": "content", "description": "The column containing text data to analyze"}, "min_topic": {"type": "integer", "default": 3, "description": "Minimum number of topics to consider"}, "max_topic": {"type": "integer", "default": 10, "description": "Maximum number of topics to consider"}, "no_below": {"type": "integer", "default": 5, "description": "Minimum document frequency for words"}, "no_above": {"type": "number", "format": "float", "default": 0.2, "description": "Maximum document frequency for words"}, "network_style": {"type": "string", "default": "academic", "enum": ["default", "academic", "publication", "minimal", "circular"], "description": "Style for network visualization"}, "chart_style": {"type": "string", "default": "default", "enum": ["default", "colorful", "minimal", "dark"], "description": "Style for topic charts"}, "manual_topic_number": {"type": "integer", "nullable": true, "description": "Optional: Force specific number of topics"}, "fast_mode": {"type": "boolean", "default": false, "description": "If true, skips some visualizations for faster processing"}}, "required": ["text_column"]}}], "responses": {"200": {"description": "LDA analysis results", "schema": {"type": "object", "description": "Result object containing LDA output", "properties": {"perplexity_plot": {"type": "string", "description": "Base64 encoded perplexity graph image"}, "coherence_plot": {"type": "string", "description": "Base64 encoded coherence graph image"}, "optimal_topic_num": {"type": "integer", "description": "Optimal number of topics found"}, "used_topic_num": {"type": "integer", "description": "Actual number of topics used"}, "topics": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "words": {"type": "string"}}}}, "topic_images": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "path": {"type": "string"}}}}, "network_img_path": {"type": "string"}, "wordcloud_paths": {"type": "array", "items": {"type": "object", "properties": {"topic_id": {"type": "integer"}, "path": {"type": "string"}, "filename": {"type": "string"}}}}, "pyldavis_html": {"type": "string", "description": "HTML string for pyLDAvis visualization"}, "csv_path": {"type": "string", "description": "Path to the results CSV file"}}}}, "400": {"description": "Missing file or parameter error", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "업로드된 파일이 없습니다"}}}}, "404": {"description": "File not found", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "파일을 찾을 수 없습니다"}}}}, "500": {"description": "Server error", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "LDA 토픽 모델링 처리 중 오류가 발생했습니다"}}}}}}}, "/api/analyse/edit_keywords": {"post": {"summary": "Edit keywords for a specific topic", "tags": ["Data Analysis"], "description": "Allows editing and removal of topic keywords and updates all visualizations accordingly.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"type": "object", "properties": {"topic_id": {"type": "integer", "example": 0}, "edited_words": {"type": "array", "items": {"type": "object", "properties": {"original": {"type": "string", "example": "old_keyword"}, "new": {"type": "string", "example": "new_keyword"}}}}, "removed_words": {"type": "array", "items": {"type": "string", "example": "obsolete_keyword"}}, "chart_style": {"type": "string", "example": "academic"}, "network_style": {"type": "string", "example": "academic"}}, "required": ["topic_id"]}}], "responses": {"200": {"description": "Updated keyword visualizations", "schema": {"type": "object", "properties": {"topics": {"type": "array", "items": {"type": "object"}}, "network_img_path": {"type": "string"}, "wordcloud_img_path": {"type": "string"}, "topic_images": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "path": {"type": "string"}}}}, "pyldavis_html": {"type": "string"}}}}, "400": {"description": "Bad request or model not loaded", "schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}, "/api/tfidf/get_word_data": {"post": {"tags": ["TF-IDF Analysis"], "summary": "Get word data for TF-IDF analysis", "description": "Returns word list data for manual selection mode in TF-IDF analysis.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to analyze"}], "responses": {"200": {"description": "Word data retrieved successfully", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "words": {"type": "array", "items": {"type": "string"}}, "session_id": {"type": "string"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Server error"}}}}, "/api/tfidf/analyze": {"post": {"tags": ["TF-IDF Analysis"], "summary": "Perform TF-IDF analysis", "description": "Main TF-IDF analysis endpoint that processes text data and generates visualizations.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to analyze"}, {"name": "selection_type", "in": "formData", "required": false, "type": "string", "default": "top_n", "description": "Selection type for words"}, {"name": "max_words", "in": "formData", "required": false, "type": "string", "default": "50", "description": "Maximum number of words"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle", "description": "Word cloud shape"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis", "description": "Word cloud color scheme"}, {"name": "selected_words", "in": "formData", "required": false, "type": "string", "description": "JSON array of selected words"}], "responses": {"200": {"description": "TF-IDF analysis completed successfully", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "session_id": {"type": "string"}, "download_url": {"type": "string"}}}}, "400": {"description": "Bad request"}, "404": {"description": "File not found"}, "500": {"description": "Server error"}}}}, "/api/tfidf/generate_wordcloud": {"post": {"tags": ["TF-IDF Analysis"], "summary": "Generate word cloud from selected words", "description": "Generates a word cloud visualization from a list of selected words.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "selected_words", "in": "formData", "required": true, "type": "string", "description": "JSON array of selected words (minimum 10 words required)"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle", "description": "Word cloud shape"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis", "description": "Word cloud color scheme"}], "responses": {"200": {"description": "Word cloud generated successfully", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "session_id": {"type": "string"}, "download_url": {"type": "string"}}}}, "400": {"description": "Bad request (minimum 10 words required)"}, "500": {"description": "Server error"}}}}, "/api/tfidf/edit_words": {"post": {"tags": ["TF-IDF Analysis"], "summary": "Edit words and regenerate word cloud", "description": "Edits words and regenerates the word cloud with the updated words.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "edited_words", "in": "formData", "required": true, "type": "string", "description": "JSON array of edited words (minimum 10 words required)"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle", "description": "Word cloud shape"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis", "description": "Word cloud color scheme"}], "responses": {"200": {"description": "Word cloud regenerated successfully", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "session_id": {"type": "string"}, "download_url": {"type": "string"}}}}, "400": {"description": "Bad request (minimum 10 words required)"}, "500": {"description": "Server error"}}}}, "/api/analyse/update_chart_style": {"post": {"summary": "Update chart style for topic visualizations", "tags": ["Data Analysis"], "description": "Updates the visualization style for topic charts and regenerates all topic visualizations.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"type": "object", "properties": {"chart_style": {"type": "string", "enum": ["default", "colorful", "minimal", "dark"], "default": "default", "description": "The style to apply to the topic charts"}}, "required": ["chart_style"]}}], "responses": {"200": {"description": "Successfully updated chart style", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "topic_images": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Topic ID"}, "path": {"type": "string", "description": "Path to the topic visualization image"}, "url": {"type": "string", "description": "Full URL to access the visualization"}}}}, "chart_style": {"type": "string", "description": "The applied chart style"}}}}, "400": {"description": "Model not loaded or invalid style", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "모델이 로드되지 않았습니다. 먼저 데이터를 처리해주세요."}}}}, "500": {"description": "Server error", "schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}, "/api/ngram/analyze": {"post": {"tags": ["N-gram Analysis"], "summary": "Perform N-gram analysis", "description": "Analyzes text data using N-gram techniques and generates word clouds.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to analyze"}, {"name": "n_gram", "in": "formData", "required": false, "type": "string", "default": "2", "description": "N-gram size (e.g., 2 for bigrams)"}, {"name": "max_features", "in": "formData", "required": false, "type": "string", "default": "3000", "description": "Maximum number of features to extract"}, {"name": "selection_type", "in": "formData", "required": false, "type": "string", "default": "manual", "description": "Selection type for words"}, {"name": "max_words", "in": "formData", "required": false, "type": "string", "description": "Maximum number of words for word cloud"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle", "description": "Word cloud shape"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis", "description": "Word cloud color scheme"}, {"name": "selected_words", "in": "formData", "required": false, "type": "string", "description": "JSON array of selected words"}], "responses": {"200": {"description": "N-gram analysis completed successfully"}, "400": {"description": "Bad request"}, "500": {"description": "Server error"}}}}, "/api/ngram/get_word_data": {"post": {"tags": ["N-gram Analysis"], "summary": "Get N-gram word data", "description": "Returns N-gram word list data for manual selection mode.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to analyze"}, {"name": "n_gram", "in": "formData", "required": false, "type": "string", "default": "2", "description": "N-gram size (e.g., 2 for bigrams)"}, {"name": "max_features", "in": "formData", "required": false, "type": "string", "default": "3000", "description": "Maximum number of features to extract"}], "responses": {"200": {"description": "N-gram word data retrieved successfully"}, "400": {"description": "Bad request"}, "500": {"description": "Server error"}}}}, "/api/ngram/generate_wordcloud": {"post": {"tags": ["N-gram Analysis"], "summary": "Generate N-gram word cloud", "description": "Generates a word cloud from selected N-gram words.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "selected_words", "in": "formData", "required": true, "type": "string", "description": "JSON array of selected words (minimum 10 words required)"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle", "description": "Word cloud shape"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis", "description": "Word cloud color scheme"}, {"name": "n_gram", "in": "formData", "required": false, "type": "string", "default": "2", "description": "N-gram size"}], "responses": {"200": {"description": "N-gram word cloud generated successfully"}, "400": {"description": "Bad request (minimum 10 words required)"}, "500": {"description": "Server error"}}}}, "/api/sentrans/analyze": {"post": {"tags": ["Sentiment Analysis"], "summary": "Perform word similarity analysis", "description": "Analyzes word similarity using transformer models and generates visualizations.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to analyze"}, {"name": "max_features", "in": "formData", "required": false, "type": "string", "default": "3000", "description": "Maximum number of features"}, {"name": "top_n_similar", "in": "formData", "required": false, "type": "string", "default": "10", "description": "Number of top similar words"}, {"name": "selection_type", "in": "formData", "required": false, "type": "string", "default": "auto", "description": "Selection type (auto, manual, specific_input, pairwise_specific_input)"}, {"name": "visualization_shape", "in": "formData", "required": false, "type": "string", "default": "circular", "description": "Visualization shape"}, {"name": "visualization_color", "in": "formData", "required": false, "type": "string", "default": "pastel", "description": "Visualization color scheme"}, {"name": "model_id", "in": "formData", "required": false, "type": "string", "default": "jhgan/ko-sbert-sts", "description": "Transformer model ID"}, {"name": "target_ngram", "in": "formData", "required": false, "type": "string", "default": "1", "description": "Target N-gram size"}, {"name": "specific_word", "in": "formData", "required": false, "type": "string", "description": "Specific word for analysis (when selection_type is specific_input)"}, {"name": "specific_word_a", "in": "formData", "required": false, "type": "string", "description": "First word for pairwise analysis"}, {"name": "specific_word_b", "in": "formData", "required": false, "type": "string", "description": "Second word for pairwise analysis"}, {"name": "selected_words", "in": "formData", "required": false, "type": "string", "description": "JSON array of selected words for manual mode"}], "responses": {"200": {"description": "Word similarity analysis completed successfully", "schema": {"type": "object", "properties": {"download_url": {"type": "string"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Server error"}}}}, "/api/sentrans/image/{filename}": {"get": {"tags": ["Sentiment Analysis"], "summary": "Get analysis result image", "description": "Retrieves an image file from the sentiment analysis results.", "parameters": [{"name": "filename", "in": "path", "required": true, "type": "string", "description": "The filename of the image to retrieve"}], "responses": {"200": {"description": "Image file", "schema": {"type": "file"}}, "500": {"description": "Image not found"}}}}, "/api/sentrans/get_models": {"get": {"tags": ["Sentiment Analysis"], "summary": "Get available embedding models", "description": "Returns a list of available transformer embedding models.", "responses": {"200": {"description": "Available models retrieved successfully", "schema": {"type": "object", "properties": {"models": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/connet/analyze": {"post": {"tags": ["Connection Analysis"], "summary": "Perform connection network analysis", "description": "Analyzes text data to create network visualizations showing word connections and clusters.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to analyze"}, {"name": "top_word_count", "in": "formData", "required": false, "type": "integer", "default": 30, "description": "Number of top words to include in analysis"}, {"name": "node_size_min", "in": "formData", "required": false, "type": "number", "default": 4000, "description": "Minimum node size"}, {"name": "node_size_max", "in": "formData", "required": false, "type": "number", "default": 8000, "description": "Maximum node size"}, {"name": "edge_width_min", "in": "formData", "required": false, "type": "number", "default": 1.0, "description": "Minimum edge width"}, {"name": "edge_width_max", "in": "formData", "required": false, "type": "number", "default": 15.0, "description": "Maximum edge width"}, {"name": "edge_color", "in": "formData", "required": false, "type": "string", "default": "#707070", "description": "Edge color"}, {"name": "edge_alpha", "in": "formData", "required": false, "type": "number", "default": 0.7, "description": "Edge transparency"}, {"name": "node_color", "in": "formData", "required": false, "type": "string", "default": "#1f78b4", "description": "Node color"}, {"name": "label_size", "in": "formData", "required": false, "type": "number", "default": 18, "description": "Label font size"}, {"name": "label_color", "in": "formData", "required": false, "type": "string", "default": "#000000", "description": "Label color"}, {"name": "label_font_weight", "in": "formData", "required": false, "type": "string", "default": "bold", "description": "Label font weight"}, {"name": "label_background", "in": "formData", "required": false, "type": "string", "default": "true", "description": "Whether to show label background"}, {"name": "layout_seed", "in": "formData", "required": false, "type": "integer", "description": "Random seed for layout (random if not provided)"}, {"name": "layout_k", "in": "formData", "required": false, "type": "number", "default": 1.3, "description": "Layout spring constant"}, {"name": "layout_iterations", "in": "formData", "required": false, "type": "integer", "default": 200, "description": "Number of layout iterations"}, {"name": "edge_filter_percent", "in": "formData", "required": false, "type": "number", "default": 30, "description": "Edge filter percentage"}, {"name": "use_concor", "in": "formData", "required": false, "type": "string", "default": "true", "description": "Whether to use CONCOR algorithm"}, {"name": "concor_n", "in": "formData", "required": false, "type": "integer", "default": 3, "description": "CONCOR n parameter"}, {"name": "concor_max_iter", "in": "formData", "required": false, "type": "integer", "default": 50, "description": "CONCOR maximum iterations"}, {"name": "concor_convergence", "in": "formData", "required": false, "type": "number", "default": 0.95, "description": "CONCOR convergence threshold"}, {"name": "color_palette", "in": "formData", "required": false, "type": "string", "default": "tableau10", "description": "Color palette for visualization"}, {"name": "use_gradients", "in": "formData", "required": false, "type": "string", "default": "false", "description": "Whether to use gradient colors"}, {"name": "use_custom_colors", "in": "formData", "required": false, "type": "string", "default": "false", "description": "Whether to use custom colors"}], "responses": {"200": {"description": "Connection analysis completed successfully"}, "400": {"description": "Bad request"}, "500": {"description": "Server error"}}}}, "/api/bert/analyze": {"post": {"tags": ["BERT Analysis"], "summary": "Perform BERT-based topic modeling", "description": "Analyzes text data using BERT embeddings for topic modeling and clustering.", "consumes": ["multipart/form-data"], "parameters": [{"name": "file", "in": "formData", "required": true, "type": "file", "description": "The file to analyze"}, {"name": "column", "in": "formData", "required": true, "type": "string", "description": "The column name containing text data"}, {"name": "nr_topics", "in": "formData", "required": false, "type": "string", "default": "auto", "description": "Number of topics (auto for automatic detection)"}, {"name": "top_n_words", "in": "formData", "required": false, "type": "string", "default": "10", "description": "Number of top words per topic"}, {"name": "min_topic_size", "in": "formData", "required": false, "type": "string", "default": "5", "description": "Minimum topic size"}, {"name": "max_ngram", "in": "formData", "required": false, "type": "string", "default": "1", "description": "Maximum N-gram size"}, {"name": "min_df", "in": "formData", "required": false, "type": "string", "default": "2", "description": "Minimum document frequency"}, {"name": "embedding_model", "in": "formData", "required": false, "type": "string", "default": "sentence-transformers/xlm-r-100langs-bert-base-nli-stsb-mean-tokens", "description": "Embedding model to use"}], "responses": {"200": {"description": "BERT analysis completed successfully"}, "400": {"description": "Bad request or invalid data"}, "500": {"description": "Server error"}}}}, "/api/analyse/update_topics": {"post": {"tags": ["LDA Analysis"], "summary": "Update LDA topics with edited keywords", "description": "Updates LDA topic model with edited keywords and regenerates visualizations.", "consumes": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"type": "object", "properties": {"edited_keywords": {"type": "object", "description": "Dictionary of edited keywords by topic ID"}}, "required": ["edited_keywords"]}}], "responses": {"200": {"description": "Topics updated successfully", "schema": {"type": "object", "properties": {"topics": {"type": "array", "items": {"type": "object"}}, "pyldavis_html": {"type": "string"}, "topic_images": {"type": "array", "items": {"type": "object"}}}}}, "400": {"description": "No LDA model found"}}}}, "/api/analyse/update_network": {"post": {"tags": ["LDA Analysis"], "summary": "Update LDA network visualization", "description": "Updates the network visualization for LDA topics with edited keywords and custom styling.", "consumes": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"type": "object", "properties": {"edited_keywords": {"type": "object", "description": "Dictionary of edited keywords by topic ID"}, "network_style": {"type": "string", "default": "academic", "description": "Network visualization style"}}}}], "responses": {"200": {"description": "Network visualization updated successfully", "schema": {"type": "object", "properties": {"network_img_path": {"type": "string"}, "network_img_url": {"type": "string"}}}}, "400": {"description": "No LDA model found"}}}}, "/api/results/{subpath}": {"get": {"tags": ["Process"], "summary": "Serve result files from user session directory", "description": "Returns a specific file from the analysis results folder associated with the current session. Redirects to the unified /files/{filepath} endpoint. Can filter by session_id.", "parameters": [{"name": "subpath", "in": "path", "required": true, "type": "string", "description": "Relative file path within the user's session-specific results folder", "example": "topic_model/topic_wordcloud_1.png"}, {"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID to filter by. If provided, serves files from that specific session."}], "produces": ["application/octet-stream"], "responses": {"200": {"description": "The requested file (e.g., image or HTML result)"}, "400": {"description": "No active session", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No valid session ID found"}}}}, "404": {"description": "File not found or session not found", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "File not found or Session with ID xyz123 not found"}}}}}}}, "/get_session_id": {"get": {"tags": ["Process"], "summary": "Get session ID", "description": "Retrieves the session ID from the user's cookies.", "responses": {"200": {"description": "Successful response", "schema": {"type": "string", "example": "Session ID: abc123"}}}}}, "/session-status": {"get": {"tags": ["Session Management"], "summary": "Get session status", "description": "Returns the current session status. If no session exists, a new one will be created. Can filter by session_id.", "parameters": [{"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID to filter by. If provided, returns status for that specific session."}], "responses": {"200": {"description": "Session status information", "schema": {"type": "object", "properties": {"active": {"type": "boolean", "description": "Whether a session is active"}, "session_id": {"type": "string", "description": "The session ID"}, "uploaded_file": {"type": "string", "description": "The name of the uploaded file, if any", "nullable": true}, "has_file": {"type": "boolean", "description": "Whether a file has been uploaded"}, "data": {"type": "object", "description": "Additional session data when filtering by session_id", "nullable": true}, "message": {"type": "string", "description": "Additional information about the session", "nullable": true}}}}, "404": {"description": "Session not found", "schema": {"type": "object", "properties": {"active": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Session with ID xyz123 not found"}}}}}}}, "/session/{session_id}": {"get": {"tags": ["Session Management"], "summary": "Get session by ID", "description": "Retrieves session data for a specific session ID.", "parameters": [{"name": "session_id", "in": "path", "required": true, "type": "string", "description": "The session ID to retrieve"}], "responses": {"200": {"description": "Session data", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "session": {"type": "object", "properties": {"active": {"type": "boolean", "example": true}, "session_id": {"type": "string", "example": "abc123def45"}, "uploaded_file": {"type": "string", "nullable": true}, "options": {"type": "object", "nullable": true}, "has_file": {"type": "boolean"}, "has_directories": {"type": "object", "properties": {"upload": {"type": "boolean"}, "result": {"type": "boolean"}}}}}}}}, "404": {"description": "Session not found", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Session with ID xyz123 not found"}}}}}}}, "/files/{filepath}": {"get": {"tags": ["Session Management"], "summary": "Serve or download a file", "description": "Unified endpoint to serve or download files from the results folder. Can filter by session_id.", "parameters": [{"name": "filepath", "in": "path", "required": true, "type": "string", "description": "Path to the file relative to the session folder"}, {"name": "download", "in": "query", "required": false, "type": "string", "enum": ["true", "false"], "default": "false", "description": "Whether to download the file as an attachment"}, {"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID to filter by. If provided, serves files from that specific session."}], "responses": {"200": {"description": "The requested file", "schema": {"type": "file"}}, "400": {"description": "No active session", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No valid session ID found"}}}}, "404": {"description": "File not found or session not found", "schema": {"type": "object", "properties": {"error": {"type": "string", "example": "File not found or Session with ID xyz123 not found"}}}}}}}, "/sessions": {"get": {"tags": ["Session Management"], "summary": "List all sessions", "description": "Retrieves information about all active sessions including metadata, file sizes, and directory status.", "responses": {"200": {"description": "Sessions retrieved successfully", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "sessions": {"type": "array", "items": {"type": "object", "properties": {"session_id": {"type": "string", "example": "abc123def4"}, "created_at": {"type": "number", "example": 1703123456.789}, "last_accessed": {"type": "number", "example": 1703123500.123}, "has_upload_dir": {"type": "boolean", "example": true}, "has_result_dir": {"type": "boolean", "example": true}, "upload_size_bytes": {"type": "integer", "example": 1048576}, "result_size_bytes": {"type": "integer", "example": 2097152}, "total_size_bytes": {"type": "integer", "example": 3145728}, "data_keys": {"type": "array", "items": {"type": "string"}, "example": ["uploaded_file", "options"]}}}}, "total_sessions": {"type": "integer", "example": 5}, "total_size_bytes": {"type": "integer", "example": 15728640}, "total_size_mb": {"type": "number", "example": 15.0}}}}, "500": {"description": "Error listing sessions", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Error listing sessions: Internal server error"}}}}}}}, "/clear-session": {"post": {"tags": ["Session Management"], "summary": "Clear session data", "description": "Clears session data and removes associated files. Supports two modes: clearing a specific session by ID or clearing all sessions at once.", "parameters": [{"name": "session_id", "in": "query", "type": "string", "required": false, "description": "Specific session ID to clear. Required if clear_all is not set to true.", "example": "abc123def4"}, {"name": "clear_all", "in": "query", "type": "boolean", "required": false, "description": "Set to true to clear all sessions. If true, session_id parameter is ignored.", "example": true}], "responses": {"200": {"description": "Session(s) cleared successfully", "schema": {"oneOf": [{"type": "object", "description": "Response for clearing a specific session", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Session abc123def4 cleared successfully"}, "session_id": {"type": "string", "example": "abc123def4"}, "directories_removed": {"type": "array", "items": {"type": "string"}, "description": "List of directories that were removed", "example": ["/path/to/uploads/abc123def4", "/path/to/results/abc123def4"]}}}, {"type": "object", "description": "Response for clearing all sessions", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Cleared 5 sessions successfully"}, "cleared_sessions": {"type": "array", "items": {"type": "string"}, "description": "List of session IDs that were cleared", "example": ["abc123def4", "xyz789ghi0", "def456jkl1"]}, "directories_removed": {"type": "array", "items": {"type": "string"}, "description": "List of directories that were removed", "example": ["/path/to/uploads/abc123def4", "/path/to/results/abc123def4"]}, "total_sessions_cleared": {"type": "integer", "example": 5}, "total_directories_removed": {"type": "integer", "example": 10}, "warnings": {"type": "array", "items": {"type": "string"}, "description": "List of non-critical warnings that occurred during cleanup", "example": ["Error removing upload directory for xyz789ghi0: Permission denied"]}}}]}}, "400": {"description": "Bad request - missing required parameters", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "No session ID provided. Use session_id parameter for specific session or clear_all=true for all sessions."}}}}, "404": {"description": "Session not found", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Session with ID abc123def4 not found"}}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Error clearing session: Internal server error"}}}}}}}, "/api/analyse/delete_words": {"post": {"tags": ["Data Analysis"], "summary": "Delete words and re-run analysis", "description": "Delete specified words from the analysis results and re-run the analysis to generate updated results and visualizations.", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "session_id", "in": "query", "required": false, "type": "string", "description": "Optional session ID. If not provided, current session will be used."}, {"name": "column_name", "in": "formData", "required": true, "type": "string", "description": "The column name to analyze"}, {"name": "words_to_delete", "in": "formData", "required": true, "type": "string", "description": "JSON array of words to delete from the analysis"}, {"name": "cloud_shape", "in": "formData", "required": false, "type": "string", "default": "rectangle", "description": "Shape of the word cloud visualization"}, {"name": "cloud_color", "in": "formData", "required": false, "type": "string", "default": "viridis", "description": "Color theme for the word cloud"}, {"name": "max_words", "in": "formData", "required": false, "type": "string", "default": "50", "description": "Maximum number of words to include"}, {"name": "selection_type", "in": "formData", "required": false, "type": "string", "default": "top_n", "description": "Word selection method (top_n or manual)"}], "responses": {"200": {"description": "Successfully deleted words and re-ran analysis", "schema": {"type": "object", "properties": {"words": {"type": "array", "items": {"type": "string"}, "description": "Updated list of words after deletion"}, "download_url": {"type": "string", "description": "URL to download the analysis results"}, "wordcloud_url": {"type": "string", "description": "URL to view the updated word cloud visualization"}}}}, "400": {"description": "Bad request (e.g., missing parameters, invalid JSON)", "schema": {"type": "object", "properties": {"error": {"type": "string"}}}}, "404": {"description": "File not found", "schema": {"type": "object", "properties": {"error": {"type": "string"}}}}, "500": {"description": "Server error", "schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}}