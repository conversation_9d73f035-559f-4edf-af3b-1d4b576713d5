{"swagger": "2.0", "info": {"version": "1.0.0", "title": "Analysis App API", "description": "API documentation for the Analysis App"}, "host": "127.0.0.1:7000", "basePath": "/api", "schemes": ["http", "https"], "tags": [{"name": "Data Collection", "description": "Endpoints for uploading and selecting columns from files"}, {"name": "Data Processing", "description": "Endpoints for processing files and analyzing text and other utility needed by the core analysis endpoints"}, {"name": "Frequency Analysis", "description": "Endpoints for word analysis and word cloud generation"}, {"name": "Process", "description": "Endpoints for managing processing tasks and downloading results"}, {"name": "Session Management", "description": "Endpoints for managing user sessions"}, {"name": "LDA Analysis", "description": "Endpoints for LDA topic modeling"}, {"name": "TF-IDF Analysis", "description": "Endpoints for TF-IDF analysis"}, {"name": "N-gram Analysis", "description": "Endpoints for generating N-gram analysis"}, {"name": "Sentiment Analysis", "description": "Endpoints for sentiment and word similarity analysis"}, {"name": "Connection Analysis", "description": "Endpoints for network and connection analysis"}, {"name": "BERT Analysis", "description": "Endpoints for BERT-based topic modeling"}], "paths": {"/api/upload": {"post": {"tags": ["Data Collection"], "summary": "Upload a file", "description": "Uploads a file and returns its columns. Creates a new session if one doesn't exist.", "parameters": [{"name": "file", "in": "formData", "required": true, "type": "file", "description": "The file to upload (CSV or Excel)."}], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "filename": {"type": "string"}, "columns": {"type": "array", "items": {"type": "string"}}, "session_id": {"type": "string", "description": "The session ID"}}}}, "400": {"description": "Bad request (e.g., no file uploaded or invalid file type)"}, "500": {"description": "Internal server error"}}}}}}